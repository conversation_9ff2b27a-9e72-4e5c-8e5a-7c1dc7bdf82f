import dynamic from 'next/dynamic';
import React, { useEffect, useState, useRef } from 'react';

import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';

import { DialogContent, DialogHeader, DialogTitle } from '../dialog';
import Loader from '../loader';
import ZoomableImage from './zoomableImage';
import CkEditor from '../ckEditor';

const PdfViewer = dynamic(() => import('../pdfViewer'), {
  ssr: false,
});
const GLTFViewer = dynamic(() => import('./gltfView'), { ssr: false });

interface IProps {
  title: string;
  filePath: string;
  extension: 'pdf' | 'html' | 'docx' | 'doc' | 'png' | 'jpeg' | 'jpg';
  dialogClass?: string;
  onScrollToBottom?: () => void;
}
const expiration = '800';

const DocumentViewModal = ({
  title,
  filePath,
  extension,
  dialogClass = '',
  onScrollToBottom,
}: IProps) => {
  const { accessToken } = useAuthStore();
  const [editorData, setEditorData] = useState('');
  const [hasScrolledToBottom, setHasScrolledToBottom] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  if (extension == 'docx') {
    filePath = filePath.replace('.docx', '.pdf');
  }

  if (extension == 'doc') {
    filePath = filePath.replace('.doc', '.pdf');
  }

  filePath = encodeURIComponent(filePath);

  const { data, isLoading, error, reFetch } = useFetch<{
    expires_in: number;
    file_path: string;
    url: string;
  }>(
    accessToken,
    `file/presigned-url?file_path=${filePath}&expiration=${expiration}`,
    {},
  );

  const [url, seturl] = useState('');

  useEffect(() => {
    if (error) {
      seturl('');
    }
    if (data) {
      seturl(data?.url);
    }
  }, [data, error]);

  // Check if scrolling is needed when content loads
  useEffect(() => {
    if (url && scrollContainerRef.current) {
      // Small delay to ensure content is rendered
      const timer = setTimeout(() => {
        if (scrollContainerRef.current) {
          checkIfScrollingNeeded(scrollContainerRef.current);
        }
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [url, hasScrolledToBottom]);

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10; // 10px threshold

    if (isAtBottom && !hasScrolledToBottom) {
      setHasScrolledToBottom(true);
      onScrollToBottom?.();
    }
  };

  // Check if content is already fully visible (no scrolling needed)
  const checkIfScrollingNeeded = (element: HTMLDivElement) => {
    if (element) {
      const { scrollHeight, clientHeight } = element;

      if (scrollHeight <= clientHeight + 10) {
        if (!hasScrolledToBottom) {
          setHasScrolledToBottom(true);
          onScrollToBottom?.();
        }
      }
    }
  };
  return (
    <DialogContent className={`${dialogClass} w-full overflow-hidden`}>
      <DialogHeader className="w-full overflow-hidden pb-2 ">
        <DialogTitle>{title}</DialogTitle>
      </DialogHeader>
      <div
        ref={scrollContainerRef}
        className="max-h-[80vh] w-full overflow-x-hidden overflow-y-auto"
        onScroll={handleScroll}
      >
        {url === '' ? (
          <Loader className="h-[300px]" />
        ) : (
          <>
            {['pdf', 'docx', 'doc'].includes(extension) ? (
              <PdfViewer url={url} />
            ) : ['png', 'jpg', 'jpeg'].includes(extension) ? (
              <ZoomableImage url={url} title={title} />
            ) : ['zip'].includes(extension) ? (
              <GLTFViewer url={url} />
            ) : (
              <CkEditorView
                url={url}
                editorData={editorData}
                setEditorData={setEditorData}
              />
            )}
          </>
        )}
      </div>
    </DialogContent>
  );
};

const CkEditorView = ({
  url,
  editorData,
  setEditorData,
}: {
  url: string;
  editorData: string;
  setEditorData: React.Dispatch<React.SetStateAction<string>>;
}) => {
  const updatedEditorData = editorData.replace(
    /<table/g,
    '<table class="table-auto border-collapse border border-gray-300 [&_th]:border [&_td]:border [&_th]:border-gray-300 [&_td]:border-gray-300 [&_td]:p-1 " border="1"',
  );
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchHtmlContent = async () => {
      try {
        setLoading(true);
        const response = await fetch(url);

        if (!response.ok) {
          throw new Error(
            `Failed to fetch HTML content. Status: ${response.status}`,
          );
          setLoading(false);
        }

        const htmlContent = await response.text();
        setEditorData(htmlContent ? `${htmlContent}` : '');
        setLoading(false);
        // Set the fetched HTML content to CKEditor
      } catch (error) {
        return;
      }
    };

    if (url) fetchHtmlContent();
  }, [setEditorData, url]);

  return (
    <div>
      {loading ? (
        <Loader className="h-[300px]" />
      ) : (
        // <div
        //   onClick={(e) => e.preventDefault()}
        //   dangerouslySetInnerHTML={{
        //     __html: updatedEditorData,
        //   }}
        //   className="ck ck-content ck-editor__editable ck-rounded-corners ck-editor__editable_inline"
        // />
        <CkEditor
          disabled={true}
          url={url}
          editorData={editorData}
          setEditorData={setEditorData}
        />
      )}
    </div>
  );
};

export default DocumentViewModal;
