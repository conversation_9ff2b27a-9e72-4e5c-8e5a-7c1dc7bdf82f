import React, { useEffect, useState, useRef } from 'react';

import { Textarea } from '@/components/common/textarea';
import { useAuthStore } from '@/globalProvider/authStore';

import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import { usePut } from '@/hooks/usePut';
import { toast } from 'react-toastify';
import { DetailsText } from '@/components/common/infoDetail';
import dynamic from 'next/dynamic';
import ZoomableImage from '@/components/common/modals/zoomableImage';
import useFetch from '@/hooks/useFetch';
import Loader from '@/components/common/loader';
import { formatDate } from '@/utils/time';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import { Download, FileX } from 'lucide-react';
import axios from 'axios';
import { htmlToPdf } from '@/utils/htmlToPdf';
import { getFileNameFromPath } from '@/utils/helper';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';
import { hasAccess } from '@/utils/roleAccessConfig';
import { AccessActions } from '@/constants/access';

interface IProps {
  setDocumentViewModal: React.Dispatch<React.SetStateAction<boolean>>;
  extension: 'pdf' | 'html' | 'docx' | 'doc' | 'png' | 'jpeg' | 'jpg';
  title: string;
  filePath: string;
  version: string;
  publishDate: string;
  status: string;
  next_review_date: string;
  review_period: string;
  source?: string;
  onScrollToBottom?: () => void;
}

const PdfViewer = dynamic(() => import('@/components/common/pdfViewer'), {
  ssr: false,
});

const expiration = '800';

const DocumentViewModalWithSidebar = ({
  setDocumentViewModal,
  extension,
  filePath,
  title,
  version,
  publishDate,
  status,
  next_review_date,
  review_period,
  source,
  onScrollToBottom,
}: IProps) => {
  const orgId =
    typeof window !== 'undefined'
      ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
      : null;

  const { accessToken, user } = useAuthStore();
  const [editorData, setEditorData] = useState('');
  const [hasScrolledToBottom, setHasScrolledToBottom] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  if (extension == 'docx') {
    filePath = filePath.replace('.docx', '.pdf');
  }

  if (extension == 'doc') {
    filePath = filePath.replace('.doc', '.pdf');
  }

  filePath = encodeURIComponent(filePath);

  const { data, isLoading, error, reFetch } = useFetch<{
    expires_in: number;
    file_path: string;
    url: string;
  }>(
    accessToken,
    `file/presigned-url?file_path=${filePath}&expiration=${expiration}`,
    {},
  );

  const [url, seturl] = useState('');

  useEffect(() => {
    if (error) {
      seturl('');
    }
    if (data) {
      seturl(data?.url);
    }
  }, [data, error]);

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10; // 10px threshold

    if (isAtBottom && !hasScrolledToBottom) {
      setHasScrolledToBottom(true);
      onScrollToBottom?.();
    }
  };

  // Check if content is already fully visible (no scrolling needed)
  const checkIfScrollingNeeded = (element: HTMLDivElement) => {
    if (element) {
      const { scrollHeight, clientHeight } = element;
      // If content fits entirely in viewport, consider it as "scrolled to bottom"
      if (scrollHeight <= clientHeight + 10) {
        if (!hasScrolledToBottom) {
          setHasScrolledToBottom(true);
          onScrollToBottom?.();
        }
      }
    }
  };

  // Check if scrolling is needed when content loads
  useEffect(() => {
    if (url && scrollContainerRef.current) {
      // Small delay to ensure content is rendered
      const timer = setTimeout(() => {
        if (scrollContainerRef.current) {
          checkIfScrollingNeeded(scrollContainerRef.current);
        }
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [url, hasScrolledToBottom]);

  const handleDownloadDocument = (
    path: string,
    documentTitle: string,
    versionNumber: any,
    extension?: string,
  ) => {
    console.log('extension', path, documentTitle, versionNumber, extension);
    const baseUrl = process.env.NEXT_PUBLIC_URL;
    const productVersion = process.env.NEXT_PUBLIC_VERSION;
    // const filePath = encodeURIComponent(path);
    axios
      .get(
        `${baseUrl}/${productVersion}/file/presigned-url?file_path=${path}&expiration=600`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
          },
        },
      )
      .then((res) => {
        fetch(res.data.url)
          .then((res) => res.blob())
          .then((res) => {
            if (extension === 'html') {
              res.text().then((textContent) => {
                htmlToPdf({
                  htmlContent: textContent,
                  filename: `${documentTitle}_v${versionNumber}.pdf`,
                });
              });
            } else {
              const link = document.createElement('a');
              link.href = window.URL.createObjectURL(res);
              link.download = getFileNameFromPath(
                path,
                documentTitle,
                versionNumber,
              );
              link.click();
            }
          });
      })
      .catch((err) => {
        console.log(err);
      });
  };

  return (
    <DialogContent className="min-w-[95%]">
      <DialogHeader>
        <DialogTitle>{title}</DialogTitle>
      </DialogHeader>
      <div className="mt-4 flex h-[80vh]">
        <div
          ref={scrollContainerRef}
          className="w-[75%] pr-4 overflow-auto"
          onScroll={handleScroll}
        >
          {url === '' ? (
            <Loader className="h-[300px]" />
          ) : (
            <>
              {['pdf', 'docx', 'doc'].includes(extension) ? (
                <PdfViewer url={url} />
              ) : ['png', 'jpg', 'jpeg'].includes(extension) ? (
                <ZoomableImage url={url} title={title ?? ''} />
              ) : ['html'].includes(extension) ? (
                <CkEditorView
                  url={url}
                  editorData={editorData}
                  setEditorData={setEditorData}
                />
              ) : (
                <div className="flex flex-col items-center justify-center text-center p-6 border border-gray-300 rounded-lg bg-gray-50 text-gray-600">
                  <FileX className="w-12 h-12 mb-3 text-gray-400" />
                  <p className="text-base font-medium">
                    {'No preview available'}
                  </p>
                  <span className="text-sm text-gray-500 mt-1">
                    Please download the file to view it.
                  </span>
                </div>
              )}
            </>
          )}
        </div>
        <div className="w-[25%] border-gray-200 overflow-auto">
          <div className="flex justify-between mb-4 items-end">
            <h4 className="text-lg font-semibold leading-7 mb-2 text-dark-300">
              Document Details
            </h4>
            {hasAccess(AccessActions.CanEditScope, user) &&
              source !== 'audit' && (
                <Tooltip>
                  <TooltipTrigger>
                    <div
                      className="w-10 h-10 flex items-center justify-center  bg-white-200 rounded-full hover:bg-white-300 cursor-pointer"
                      onClick={() =>
                        handleDownloadDocument(
                          filePath,
                          title,
                          version,
                          extension,
                        )
                      }
                    >
                      <Download className="h-6 w-6" />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="text-sm text-dark-300">Download</div>
                  </TooltipContent>
                </Tooltip>
              )}
          </div>
          <div className="border border-grey-100 bg-white p-4 rounded-lg mb-4">
            <h4 className="text-lg font-semibold leading-7 mb-2 text-dark-300">
              Published Version Details
            </h4>
            <DetailsText label="Version" value={version} />
            <DetailsText
              label="Date of Publish"
              value={formatDate(publishDate, false)}
            />
            <DetailsText label="Status" value={status} />
          </div>

          <div className="border border-grey-100 bg-white p-4 rounded-lg">
            <h4 className="text-lg font-semibold leading-7 mb-2 text-dark-300">
              Document Information
            </h4>
            <DetailsText
              label="Next Review Date"
              value={formatDate(next_review_date, false)}
            />
            <DetailsText label="Review Period" value={review_period} />
          </div>
        </div>
      </div>
    </DialogContent>
  );
};

const CkEditorView = ({
  url,
  editorData,
  setEditorData,
}: {
  url: string;
  editorData: string;
  setEditorData: React.Dispatch<React.SetStateAction<string>>;
}) => {
  const updatedEditorData = editorData.replace(
    /<table/g,
    '<table class="table-auto border-collapse border border-gray-300 [&_th]:border [&_td]:border [&_th]:border-gray-300 [&_td]:border-gray-300 [&_td]:p-1 " border="1"',
  );
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchHtmlContent = async () => {
      try {
        setLoading(true);
        const response = await fetch(url);

        if (!response.ok) {
          throw new Error(
            `Failed to fetch HTML content. Status: ${response.status}`,
          );
          setLoading(false);
        }

        const htmlContent = await response.text();
        setEditorData(htmlContent ? `${htmlContent}` : '');
        setLoading(false);
        // Set the fetched HTML content to CKEditor
      } catch (error) {
        return;
      }
    };

    if (url) fetchHtmlContent();
  }, [setEditorData, url]);

  return (
    <div>
      {loading ? (
        <Loader className="h-[300px]" />
      ) : (
        <div
          onClick={(e) => e.preventDefault()}
          dangerouslySetInnerHTML={{
            __html: updatedEditorData,
          }}
          className="ck ck-content ck-editor__editable ck-rounded-corners ck-editor__editable_inline"
        />
      )}
    </div>
  );
};

export default DocumentViewModalWithSidebar;
