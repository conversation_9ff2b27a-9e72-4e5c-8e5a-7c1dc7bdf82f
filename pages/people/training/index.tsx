import AdvancedTable from '@/components/common/advancedTable';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import { Dialog, DialogTrigger } from '@/components/common/dialog';
import Layout from '@/components/common/sidebar/layout';
import CreateTrainingModal from '@/components/training/createTrainingModal';
import AssignEmployeesModal from '@/components/training/assignEmployeesModal';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { formatDate } from '@/utils/time';
import { PencilIcon, Plus, TrashIcon, UserPlus } from 'lucide-react';
import { useRouter } from 'next/router';
import React, { useState, useEffect } from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import Link from 'next/link';
import SecondaryButton from '@/components/common/button/secondaryButton';
import SettingIcon from '@/assets/outline/settting';
import Tabs from '@/components/common/tabs';
import AssignTrainingModal from '@/components/training/assignTrainingModal';
import DeleteButton from '@/components/common/button/deleteButton';
import DeleteModal from '@/components/common/modals/deleteModal';
import { useDelete } from '@/hooks/useDelete';
import { toast } from 'react-toastify';
import { hasAccess } from '@/utils/roleAccessConfig';
import { AccessActions } from '@/constants/access';

function Training() {
  const [createTraining, setCreateTraining] = useState(false);
  const [assignEmplyoee, setAssignEmplyoee] = useState(false);
  const [assignTraining, setAssignTraining] = useState(false);
  const [deleteTarget, setDeleteTarget] = useState<{
    trainingId: string;
    userId: string;
    userName: string;
  } | null>(null);

  const [selectedTrainingId, setSelectedTrainingId] = useState<string>('');
  const [selectedTrainingTitle, setSelectedTrainingTitle] =
    useState<string>('');
  const router = useRouter();
  const { accessToken, user } = useAuthStore();
  const [filterKey, setFilterKey] = useState<string | undefined>(undefined);
  const [activeTab, setActiveTab] = useState<number>(0);

  type TrainingData = { records: any[] };

  const endpoint =
    filterKey === 'assignments' ? 'trainings/assignments/' : 'trainings';

  const {
    deleteData,
    isLoading: deleteLoading,
    response: deleteResponse,
    error: deleteError,
  } = useDelete();

  const { data, isLoading, error, reFetch } = useFetch(
    accessToken as string,
    endpoint,
    {},
  ) as {
    data: TrainingData;
    isLoading: boolean;
    error: any;
    reFetch: () => void;
  };

  // const handleAssignTraining = (trainingId: string, trainingTitle: string) => {
  //   setSelectedTrainingId(trainingId);
  //   setSelectedTrainingTitle(trainingTitle);
  //   setAssignTraining(true);
  // };

  const breadcrumbData = [
    {
      name: 'People hub',
      link: '/people',
    },
    {
      name: 'Training Admin',
      link: '#',
    },
  ];

  const tabsData = [
    {
      name: `Trainings`,
      textColor: 'text-dark-100',
      filter: 'trainings',
      onClick: () => setFilterKey('trainings'),
    },
    {
      name: `Assignments`,
      textColor: 'text-dark-100',
      filter: 'assignments',
      onClick: () => setFilterKey('assignments'),
    },
  ];

  useEffect(() => {
    if (router.query.filter) {
      const queryKey = router.query.filter as string;
      const matchedTabIndex = tabsData.findIndex(
        (tab) => tab.filter === queryKey,
      );
      if (matchedTabIndex !== -1) {
        setActiveTab(matchedTabIndex);
        setFilterKey(queryKey);
      }
    }
  }, [router.query.filter]);

  useEffect(() => {
    if (router.isReady && filterKey !== undefined) {
      const currentQuery = router.query;
      router.push(
        {
          pathname: '/people/training',
          query: {
            ...currentQuery,
            filter: filterKey,
          },
        },
        undefined,
        { shallow: true },
      );
    }
  }, [filterKey, router.isReady]);

  useEffect(() => {
    if (filterKey === undefined && router.isReady && !router.query.filter) {
      setFilterKey('trainings'); // default tab
    }
  }, [router.isReady, router.query.filter, filterKey]);

  useEffect(() => {
    if (deleteResponse) {
      toast.success('Training unassigned successfully');
      setDeleteTarget(null);
    }
    if (deleteError) {
      toast.error('Failed to unassigned');
      setDeleteTarget(null);
    }
  }, [deleteResponse, deleteError]);

  const trainingColumns: any[] = [
    {
      key: 'title',
      label: 'Title',
      sortable: true,
      resizable: true,
      width: 350,
      showFilter: false,
      render: (value: any, row: any) => {
        return (
          <div
            className="flex flex-col"
            onClick={() =>
              router.push({
                pathname: `/people/training/${row?.id}`,
                query: router.query,
              })
            }
          >
            <div className="flex items-center gap-2">
              <span className="text-[#0C66E4] font-semibold cursor-pointer">
                {row?.title}
              </span>
            </div>
            {row?.description && (
              <span className="text-gray-500 text-xs font-semibold overflow-hidden text-ellipsis">
                {row.description}
              </span>
            )}
          </div>
        );
      },
    },
    {
      key: 'training_type',
      label: 'Type',
      sortable: true,
      resizable: true,
      width: undefined,
      showFilter: true,
      render: (value: any, row: any) => {
        return (
          row?.training_type && (
            <p className="capitalize">{row?.training_type}</p>
          )
        );
      },
    },
    {
      key: 'duration',
      label: 'Duration',
      sortable: true,
      resizable: true,
      width: undefined,
      showFilter: false,
    },
    {
      key: 'status',
      label: 'Status',
      sortable: false,
      resizable: true,
      width: undefined,
      showFilter: true,
    },

    {
      key: 'assigned_user_count',
      label: 'Assigned',
      sortable: true,
      resizable: true,
      width: undefined,
      showFilter: false,
      render: (value: any, row: any) => {
        return (
          row?.assigned_user_count && `${row?.assigned_user_count} employees`
        );
      },
    },
    {
      key: 'percent_completion',
      label: 'Completion',
      sortable: false,
      resizable: true,
      width: undefined,
      showFilter: false,
      render: (value: any, row: any) => {
        return row?.percent_completion && `${row?.percent_completion} %`;
      },
    },
    // {
    //   key: 'actions',
    //   label: 'Actions',
    //   sortable: false,
    //   resizable: true,
    //   width: 120,
    //   showFilter: false,
    //   render: (value: any, row: any) => {
    //     return row?.status === 'Published' &&
    //       hasAccess(AccessActions.CanAddOrEditPeople, user) ? (
    //       <button
    //         onClick={(e) => {
    //           e.stopPropagation();
    //           handleAssignTraining(row.id, row.title);
    //         }}
    //         className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition"
    //         title="Assign Training"
    //       >
    //         <UserPlus size={20} />
    //       </button>
    //     ) : (
    //       '-'
    //     );
    //   },
    // },
  ];

  const assignmentColumns: any[] = [
    {
      key: 'user',
      label: 'Employee',
      sortable: true,
      resizable: true,
      width: 350,
      showFilter: false,
      render: (value: any, row: any) => {
        return (
          <div
            className="flex flex-col"
            // onClick={() =>
            //   router.push({
            //     pathname: `/people/training/${row?.id}`,
            //     query: router.query,
            //   })
            // }
          >
            <div className="flex items-center gap-2">
              <span className="text-[#0C66E4] font-semibold cursor-pointer">
                {row?.user?.name}
              </span>
            </div>
            {row?.user && (
              <span className="text-gray-500 text-xs font-semibold overflow-hidden text-ellipsis">
                {row?.user?.employee_id}
              </span>
            )}
          </div>
        );
      },
    },
    { key: 'training.title', label: 'Training', sortable: true },

    {
      key: 'assigned_on',
      label: 'Assigned Date',
      render: (v: any) => formatDate(v),
    },
    {
      key: 'due_date',
      label: 'Due Date',
      sortable: true,
      render: (v: any) => formatDate(v),
    },
    { key: 'status', label: 'Status', sortable: true },
    {
      key: 'completion',
      label: 'Completion',
    },
    {
      key: 'score',
      label: 'Score',
      sortable: false,
    },
    {
      key: 'actions',
      label: 'Actions',
      sortable: false,
      resizable: true,
      width: 120,
      showFilter: false,
      render: (value: any, row: any) => {
        return hasAccess(AccessActions.CanAddOrEditPeople, user) &&
          row.status !== 'Completed' ? (
          <button
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              setDeleteTarget({
                trainingId: row?.training?.id,
                userId: row?.user?.id,
                userName: row?.user?.name,
              });
            }}
            className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition"
            title="Unassign Training"
          >
            <TrashIcon height={20} />
          </button>
        ) : (
          '-'
        );
      },
    },
  ];

  return (
    <Layout>
      <div className="">
        <div className="flex flex-col my-5">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-3xl leading-10">
            Training Adminstration
          </div>
          {/* <div className="text-dark-100 font-normal text-base leading-8">
            Manage training content and assignments
          </div> */}
        </div>
        <div className="mt-2">
          <Tabs
            tabsData={tabsData}
            activeTab={activeTab}
            setActiveTab={setActiveTab}
          />
        </div>
        <div>
          <div>
            <AdvancedTable
              data={data?.records ?? []}
              columns={
                filterKey === 'assignments'
                  ? assignmentColumns
                  : trainingColumns
              }
              loading={isLoading}
              sortable
              resizable
              pagination
              searchRightSideElement={
                filterKey === 'trainings' ? (
                  hasAccess(AccessActions.CanAddOrEditPeople, user) ? (
                    <div className="flex gap-4">
                      <Tooltip>
                        <TooltipTrigger>
                          <Link href={'/people/training-adminstration'}>
                            <SecondaryButton
                              icon={<SettingIcon />}
                              text=""
                              size="medium"
                              buttonClasses="!p-2.5"
                            />
                          </Link>
                        </TooltipTrigger>
                        <TooltipContent>Training Administration</TooltipContent>
                      </Tooltip>
                      <Dialog
                        open={createTraining}
                        onOpenChange={setCreateTraining}
                      >
                        <DialogTrigger asChild>
                          <PrimaryButton
                            size="medium"
                            text="Add Training"
                            icon={<Plus size={20} />}
                            iconPosition="left"
                          />
                        </DialogTrigger>
                        {createTraining && (
                          <CreateTrainingModal
                            open={createTraining}
                            onOpenChange={setCreateTraining}
                            refetch={reFetch}
                          />
                        )}
                      </Dialog>
                    </div>
                  ) : undefined
                ) : hasAccess(AccessActions.CanAddOrEditPeople, user) ? (
                  <div className="flex gap-4">
                    <Dialog
                      open={assignEmplyoee}
                      onOpenChange={setAssignEmplyoee}
                    >
                      <DialogTrigger asChild>
                        <PrimaryButton
                          size="medium"
                          text="Assign Training"
                          icon={<Plus size={20} />}
                          iconPosition="left"
                        />
                      </DialogTrigger>
                      {assignEmplyoee && !isLoading && (
                        <AssignTrainingModal
                          open={assignEmplyoee}
                          onOpenChange={setAssignEmplyoee}
                          assignmentsData={data}
                          onSuccess={reFetch}
                        />
                      )}
                    </Dialog>
                  </div>
                ) : undefined
              }
            />
          </div>
        </div>

        {/* Assign Training Modal */}
        <AssignEmployeesModal
          open={assignTraining}
          onOpenChange={setAssignTraining}
          trainingId={selectedTrainingId}
          trainingTitle={selectedTrainingTitle}
          refetch={reFetch}
        />

        <Dialog
          open={!!deleteTarget}
          onOpenChange={(open) => {
            if (!open) setDeleteTarget(null);
          }}
        >
          <DeleteModal
            title="Delete Assignment"
            infoText={`Are you sure you want to unassign this training to ${deleteTarget?.userName}?`}
            btnText="Delete"
            onClick={async () => {
              if (!deleteTarget) return;
              await deleteData(
                accessToken as string,
                `trainings/${deleteTarget.trainingId}/users/${deleteTarget.userId}`,
              );
              setDeleteTarget(null);
              reFetch();
            }}
            dialogContentClass="min-w-[33.375rem]"
            btnLoading={deleteLoading}
          />
        </Dialog>
      </div>
    </Layout>
  );
}

export default Training;
